const cmp = require('semver-compare');
const { DateTime } = require('luxon');
const _ = require('underscore');
const lodash = require('lodash');
const moment = require('moment');
const momentTz = require('moment-timezone');
const locale = require('locale-codes');
const User = require('../models/user');
const BannedUser = require('../models/banned-user');
const BannedSource = require('../models/banned-source');
const premiumLib = require('./premium');
const constants = require('./constants');
const personalityLib = require('./personality');
const promptsLib = require('./prompts');
const locationLib = require('./location');
const reportLib = require('./report');
const { formatInterests } = require('./interest');
const languageLib = require('./languages');
const { getFullCountryName } = require('./location');
const { admin, sendNotification } = require('../config/firebase-admin');
const countryLib = require('../lib/country');
const openaiLib = require('./openai');
const stripe = require('../lib/stripe').stripe;
const { getPricingConfig, getCountryCode, getVariant } = require('../lib/stripe-prices');
const { isDisposable } = require('../lib/disposable-email');
const SignupRejected = require('../models/signup-rejected');
const UsersProfileAnalysisPrompt = require('../models/users-profile-analysis-prompts')
const ExclusionListDetailed = require('../models/exclusion-list-detailed')
const { sendSocketEvent, grantVerifyProfileAward } = require('../lib/socket');
const { translate, translate_frontend } = require('../lib/translate');
const { sendVerificationFailedEmail, sendVerificationSuccessfulEmail } = require('../lib/email');
const { updateUserScore } = require('../lib/score');
const { religionsBatch2 } = require('../lib/moreAboutUser');
const UserEmbeddings = require('../models/user-embeddings');
const UsersAiTailoredPrompts = require('../models/users-ai-tailored-prompts');
const AiFilter = require('../models/ai-filters');
const { generateEmbedding, mergeEmbeddings, isTokenCountValidForClip } = require('../lib/aifilter/helper');
const { getAifilterTranslation, getAiTailoredPromptsForUser } = require('./openai');
const AIImage = require("../models/ai-image");
const {batchStatus} = require('./ai-image/const')
const { includeIfValid } = require('./basic');
const Report = require('../models/report');
const PreemptiveModerationLog = require('../models/preemptive-moderation-log');
const { removeBannedKeywords } = require('./report-constants');
const reportConstants = require('../lib/report-constants');
const StAppRankingSnapshot = require('../models/st-app-ranking-snapshot');

User.setSendSocketEventForDeleteChat(sendSocketEvent); // added to avoid circular dependency in user models file

function formatBanNotice(user) {
  let banNotice;
  if (user.shadowBanned && user.banNotice) {
    banNotice = {
      reason: translate(user.banNotice.reason, user.locale),
      appealStatus: user.banNotice.appealStatus,
    }

    if (user.banNotice.reason.includes(',')) {
      const lf = new Intl.ListFormat(user.locale, {
        type: 'conjunction',
        style: 'narrow',
      });
      const translatedReasons = user.banNotice.reason.split(',').map(x => translate(x.trim(), user.locale));
      banNotice.reason = lf.format(translatedReasons);
      banNotice.reasons = translatedReasons;
    }

    const appealRejectedRecord = lodash.findLast(user.banHistory, (x) => x.action == 'appealRejected' && x.by == null);
    if (appealRejectedRecord && moment().diff(appealRejectedRecord.date, 'hours') < 6) {
      banNotice.appealStatus = 'pending';
    }
  }
  return banNotice;
}

async function formatMyProfile(user, userMetadata, ipAddress) {
  const preferences = premiumLib.getPreferences(user);
  const { socialPreferences } = user;

  if (!user.isVerified()) {
    if (preferences.showVerifiedOnly) {
      preferences.showVerifiedOnly = false;
    }
    if (socialPreferences && socialPreferences.showVerifiedOnly) {
      socialPreferences.showVerifiedOnly = false;
    }
  }

  let horoscope = null;
  if (user.horoscope) {
    horoscope = user.horoscope;
  }

  const receivedFreeTrialFromBackend = user.metrics.receivedFreeTrialFromBackend
    && user.metrics.receivedFreeTrialFromBackendExpirationDate > new Date();

  let verificationStatus = user.verification.status;
  if (!user.versionAtLeast('1.11.6') && verificationStatus == 'reverifying') {
    verificationStatus = 'pending';
  }
  if (user.verification.updatedAt > Date.now()) {
    verificationStatus = 'pending';
  }

  let unlimitedLikesExpiration;
  if (user.unlimitedLikesExpiration > new Date()) {
    unlimitedLikesExpiration = user.unlimitedLikesExpiration;
  }
  let unlimitedDmsExpiration;
  if (user.unlimitedDmsExpiration > new Date()) {
    unlimitedDmsExpiration = user.unlimitedDmsExpiration;
  }
  let boostExpiration;
  if (user.boostExpiration > new Date()) {
    boostExpiration = user.boostExpiration;
  }

  let hideLocation;
  if (premiumLib.isPremium(user) && user.hideLocation) {
    hideLocation = true;
  }

  let hideProfileViews;
  if (premiumLib.isPremiumV1OrGodMode(user) && user.hideProfileViews) {
    hideProfileViews = true;
  }

  let prompts;
  if (user.originalFields.prompts && user.originalFields.prompts.length) {
    prompts = user.originalFields.prompts;
  } else {
    prompts = user.prompts;
  }
  if (!user.versionAtLeast('1.11.42')) {
    prompts = promptsLib.getFormattedPrompts(prompts, user.locale);
  }
  prompts = promptsLib.filterPrompts(prompts, user);

  const tempBanData = {};

  if (user.versionAtLeast('1.11.45') && user.tempBanEndAt && user.tempBanEndAt > Date.now()) {
    tempBanData.tempBanReason = user.tempBanReason;
    tempBanData.tempBanEndAt = user.tempBanEndAt;
  }

  let stickerPackPurchases;

  if (user.versionAtLeast('1.11.45')) {
    stickerPackPurchases = user.stickerPackPurchases || [];
  }

  let webFlashSale;
  let premiumFlashSale = premiumLib.getPremiumFlashSale(user);
  if (premiumFlashSale) {
    const stripeProducts = await getPricingConfig(user, ipAddress);
    for (const [key, value] of Object.entries(stripeProducts)) {
      if (value.discount) {
        webFlashSale = premiumFlashSale;
        break;
      }
    }
  }

  let premiumV2 = premiumLib.isPremiumV2(user);
  let godMode = premiumLib.isGodMode(user);
  let premium = premiumLib.isPremiumV1(user) || premiumV2 || godMode;
  let productIdPurchased;
  if (premium || premiumV2 || godMode) {
    productIdPurchased = user.productIdPurchased;
  }

  let numDbUploads; let dbUploadCoinsReceived; let
    dbUploadKarmaReceived;
  if (user.versionAtLeast('1.11.59')) {
    numDbUploads = user.numDbUploads;
    dbUploadCoinsReceived = user.dbUploadCoinsReceived;
    dbUploadKarmaReceived = user.dbUploadKarmaReceived;
  }
  let rejectionReason;
  if(user.versionAtLeast('1.11.79')){
    rejectionReason = user.verification.rejectionReason;
    if (rejectionReason == 'This version of the app is no longer supported. Please update to the latest version of Boo. Thank you!') {
      rejectionReason = translate(rejectionReason, user.locale);
    }
  }

  let deletionScheduledDate;
  if (user.deletionRequestDate) {
    deletionScheduledDate = DateTime.fromJSDate(user.deletionRequestDate).plus({ days: 30 }).toJSDate();
  }

  let incomingRequestsPreferences;
  if (user.versionAtLeast('1.13.16')){
    incomingRequestsPreferences = user.incomingRequestsPreferences;
  }

  let numBooAINeurons, aiSettings;
  if (user.versionAtLeast('1.13.17')){
    numBooAINeurons = user.numBooAINeurons;
    aiSettings = JSON.parse(JSON.stringify(user.aiSettings));
    if (!aiSettings.outputLanguage) {
      aiSettings.outputLanguage = user.locale;
    }
  }

  let numBoosts;
  if (user.versionAtLeast('1.13.50')){
    numBoosts = user.numBoosts;
  }

  let ethnicities;
  if (user.versionAtLeast('1.13.24')){
    ethnicities = user.ethnicities;
  }

  let interestPoints;
  if (user.versionAtLeast('1.13.35')) {
    interestPoints = user.interestPoints;
  }

  let sexuality;
  let sexualityVisibility;
  if (user.versionAtLeast('1.13.59')) {
    sexuality = user.sexuality;
    sexualityVisibility = user.sexualityVisibility;
  }

  let partnerCampaign;
  if (user.versionAtLeast('1.13.65') && moment().diff(user.createdAt, 'days') < 14) {
    partnerCampaign = user.partnerCampaign;
  }

  let moreAboutUser;
  if (user.versionAtLeast('1.11.44')) {
    moreAboutUser = { ... user.moreAboutUser || {} };
    if (!user.versionAtLeast('1.13.74') && religionsBatch2.includes(moreAboutUser.religion)) {
      moreAboutUser.religion = undefined;
    }
    moreAboutUser = Object.fromEntries(
      Object.entries(moreAboutUser).filter(([_, v]) => v !== undefined)
    );

    if (typeof moreAboutUser == 'object' && Object.keys(moreAboutUser).length === 0) {
      moreAboutUser = null;
    }
  }

  let excludeNotificationFields;
  let pushNotificationSettings = {...user.pushNotificationSettings}
  if (user.versionAtLeast('1.13.71')) {
    excludeNotificationFields = ['commentLikes', 'commentReplies', 'dailyPush'];
  } else {
    excludeNotificationFields = [
      'commentLikesMatches',
      'commentLikesOtherSouls',
      'commentRepliesMatches',
      'commentRepliesOtherSouls',
      'dailyFacts',
      'questionOfTheDay',
      'newSoulsNearby',
      'friendStories'
    ];
  }
  Object.keys(pushNotificationSettings).forEach(key => {
    if (excludeNotificationFields.includes(key)) {
      delete pushNotificationSettings[key];
    }
  });

  let languages = user.languages;
  if (languages && languages.length && !user.versionAtLeast('1.13.74')) {
    languages = languages.filter(x => !languageLib.languageCodesBatch2.includes(x));
  }
  if (languages && languages.length && !user.versionAtLeast('1.13.75')) {
    languages = languages.filter(x => !languageLib.languageCodesBatch3.includes(x) && !languageLib.languageCodesBatch4.includes(x));
  }

  /*
  let aiImages = [];
  if (user.config?.['app_162']) {
    aiImages = await AIImage.find(
      {
        user: user._id,
        batchStatus: { $nin: [batchStatus.SELECTED, batchStatus.EXPIRED, batchStatus.REGENERATED, batchStatus.REMOVED]},
        createdAt: { $gte: new Date(Date.now() - 72 * 60 * 60 * 1000) }
      },
      {
        _id: 1,
        batchStatus: 1,
        imageKey: "$originalImage"
      }
    );
  }
  */

  let banNotice = formatBanNotice(user);
  if (user.versionAtLeast('1.13.87') && !banNotice) {
    banNotice = null;
  }

  let profileTempBanReason = user.profileTempBanReason;
  if (profileTempBanReason == 'banned keywords found in profile') {
    profileTempBanReason = 'Spam, Promotion or Solicitation';
  }

  let profileTempBanReviewPending;
  if (user.versionAtLeast('1.13.92') && user.tempBanReviewPendingUntil && user.tempBanReviewPendingUntil > Date.now()) {
    profileTempBanReviewPending = true;
  }

  return {
    _id: user._id,
    email: user.email,
    createdAt: user.createdAt,
    firstName: user.firstName,
    ...(user.versionAtLeast('1.13.74') && { anonymousProfileNickname: user.anonymousProfileNickname }),
    birthday: user.birthday,
    gender: user.gender ? user.gender : null,
    age: user.birthday ? moment().diff(user.birthday, 'years') : null,
    height: user.height,
    ethnicities: ethnicities ? ethnicities : undefined,
    horoscope,
    description: user.originalFields.description || user.description,
    audioDescription: user.audioDescription,
    audioDescriptionWaveform: user.audioDescriptionWaveform,
    audioDescriptionDuration: user.audioDescriptionDuration,
    preferences,
    incomingRequestsPreferences,
    socialPreferences,
    socialPreferencesActivated: user.socialPreferencesActivated,
    customFeeds: user.customFeeds,
    pictures: user.originalPictures || user.pictures,
    ...(user.isConfigTrue('app_798')
      ? includeIfValid(user.privatePictures, 'privatePictures')
      : {}),
    personality: personalityLib.getPersonality(user, user.locale),
    education: user.originalFields.education || user.education,
    work: user.originalFields.work || user.work,
    enneagram: user.enneagram,
    relationshipStatus: user.relationshipStatus,
    datingSubPreferences: user.datingSubPreferences,
    relationshipType: user.relationshipType,
    teleport: !!user.teleportLocation,
    teleportCity: user.teleportCity,
    teleportState: user.teleportState,
    actualLocation: locationLib.getFormattedActualLocationFromUser(user),
    ...locationLib.getMyLocationFields(user),
    prompts,
    crown: premiumLib.isCrown(user),
    handle: user.handle ? user.handle : null,
    searchable: user.searchable,
    hidden: user.hidden,
    hideCity: user.hideCity ? user.hideCity : false,
    hideReadReceipts: (premiumLib.isPremiumV1OrGodMode(user) && user.hideReadReceipts) ? user.hideReadReceipts : false,
    hideQuestions: user.hideQuestions,
    hideComments: user.hideComments,
    hideHoroscope: user.hideHoroscope || false,
    hideLocation,
    hideProfileViews,
    ...(user.versionAtLeast('1.11.0')) ? { hiddenContacts: user.hiddenContacts || false } : {},
    ...tempBanData,
    ...includeIfValid(profileTempBanReason, 'profileTempBanReason'),
    ...includeIfValid(user.profileTempBanInfringingText, 'profileTempBanInfringingText'),
    ...includeIfValid(user.profileTempBanInfringingPictures, 'profileTempBanInfringingPictures'),
    ...includeIfValid(profileTempBanReviewPending, 'profileTempBanReviewPending'),
    moreAboutUser,
    darkMode: user.darkMode,
    useMetricSystem: user.useMetricSystem,
    vibrationsDisabled: user.vibrationsDisabled,
    dataSaver: user.dataSaver,
    dataSaving: user.dataSaving,
    hideMyFollowerCount: user.hideMyFollowerCount,
    hideMyAwards: user.hideMyAwards,
    hideMyKarma: user.hideMyKarma,
    hideMyAge: user.hideMyAge && premiumLib.isPremium(user),
    changeHomeScreenToSocial: user.changeHomeScreenToSocial,
    messagesTheme: user.messagesTheme,
    instantMatch: user.instantMatchEnabled ? user.instantMatchEnabled : false,
    deletionRequestDate: user.deletionRequestDate,
    deletionScheduledDate,
    premium,
    premiumV2,
    godMode,
    productIdPurchased,
    unlimitedLikesExpiration,
    unlimitedDmsExpiration,
    boostExpiration,
    admin: user.admin,
    adminPermissions: user.adminPermissions,
    coins: userMetadata.coins,
    interplanetaryMode: premiumLib.isInterplanetary(user),
    showFreeTrialOption: false,
    premiumFlashSale,
    webFlashSale,
    pushNotificationSettings,
    wingmanSettings: user.wingmanSettings,
    interests: formatInterests(user, user.locale),
    interestNames: user.interestNames,
    hiddenInterests: user.hiddenInterests,
    karma: Math.round(user.karma),
    approveAllFollowers: user.approveAllFollowers,
    autoFollowLikes: user.autoFollowLikes,
    autoFollowMatches: user.autoFollowMatches,
    numFollowers: user.metrics.numFollowers,
    numFollowing: user.metrics.numFollowing,
    numFollowRequests: user.metrics.numFollowRequests,
    numProfileViews: user.metrics.numProfileViews,
    numPendingLikes: user.metrics.numPendingLikes,
    verified: user.verification.status == 'verified',
    verificationStatus,
    languages: languages,
    receivedFreeTrialFromBackend: receivedFreeTrialFromBackend || undefined,
    awards: user.awards,
    spotify: user.spotify,
    stickerPackPurchases,
    numSuperLikes: user.numSuperLikes + user.numSuperLikesFree,
    numBooAINeurons,
    numBoosts,
    numDbUploads,
    dbUploadCoinsReceived,
    dbUploadKarmaReceived,
    rejectionReason,
    optOutOfAdTargeting: user.optOutOfAdTargeting,
    autoplay: user.autoplay,
    hideFromKeywords: user.hideFromKeywords,
    hideFromNearby: user.hideFromNearby,
    ...(user.versionAtLeast('1.13.88') && { hideUnverifiedUsers: user.hideUnverifiedUsers }),
    customPersonalityCompatibility: user.customPersonalityCompatibility,
    aiSettings,
    interestPoints,
    sexuality,
    sexualityVisibility,
    partnerCampaign,
    showMyInfinityStatus: user.showMyInfinityStatus,
    aiFilter: user.aiFilter,
    aiimages: null,
    aiGenPictures: null,
    ...(user.changedBirthday && { changedBirthday: true }),
    ...(user.versionAtLeast('1.13.85') && { hideFromVisionSearch: user.hideFromVisionSearch }),
    banNotice,
    accountRestrictions: user.accountRestrictions || undefined,
  };
}

async function processDeviceInfo(req, user) {
  if (req.body.appVersion) {
    if (!user.appVersion) {
      user.signupAppVersion = req.body.appVersion.trim();
    }
    user.appVersion = req.body.appVersion;
    if (user.appVersion && cmp(user.appVersion, '1.10.20') >= 0
      && user.instantMatchEnabled == undefined) {
      user.instantMatchEnabled = true;
    }
    if (user.appVersion && cmp(user.appVersion, '1.10.22') >= 0
      && user.preferences.local === undefined) {
      user.preferences.local = true;
      user.preferences.global = user.preferences.distance >= constants.maxDistanceFilter;
      user.preferences.distance = null;
    }
  }
  if (user.preferences.local !== undefined && user.preferences.distance) {
    user.preferences.distance = null;
  }
  if (user.preferences.distance) {
    user.preferences.global = user.preferences.distance >= constants.maxDistanceFilter;
  }
  if (user.preferences.gender && user.preferences.gender.length > 0) {
    user.preferences.dating = [];
    user.preferences.friends = [];
    if (!user.preferences.purpose || user.preferences.purpose.includes('dating')) {
      user.preferences.dating = user.preferences.gender;
    }
    if (!user.preferences.purpose || user.preferences.purpose.includes('friends')) {
      user.preferences.friends = user.preferences.gender;
    }
  }
  if (req.body.os && constants.validOs.includes(req.body.os)) {
    user.os = req.body.os;
    if (!user.signupSource) {
      user.signupSource = user.os;
    }
    if (user.os == 'android') {
      user.metrics.lastSeenAndroid = new Date();
    }
    if (user.os == 'ios') {
      user.metrics.lastSeenIos = new Date();
    }
    if (user.os == 'web') {
      if (user.gender == 'female' && user.verification.method == 'pose' && !user.metrics.lastSeenWeb) {
        await reportLib.createReport(
          user,
          null,
          ['Auto-report due to web pose verification'],
        );
      }
      user.metrics.lastSeenWeb = new Date();
    }
  }
  if (req.body.osVersion) { user.osVersion = req.body.osVersion; }
  if (req.body.phoneModel) { user.phoneModel = req.body.phoneModel; }
  if (req.body.isPhysicalDevice !== undefined) {
    /*
    if (user.isPhysicalDevice != false && req.body.isPhysicalDevice == false) {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report due to using emulator'],
        'emulator',
      );
    }
    */
    user.isPhysicalDevice = req.body.isPhysicalDevice;
  }
  if (req.body.jailbroken) { user.jailbroken = req.body.jailbroken; }
  if (req.body.darkMode !== undefined) {
    user.darkMode = req.body.darkMode;
  }
  // accept useMetric system here (this function will be called during initApp as well)
  if (req.body.useMetricSystem !== undefined) {
    user.useMetricSystem = req.body.useMetricSystem;
  }

  if (req.body.deviceLanguage && languageLib.languageCodes.includes(req.body.deviceLanguage)) {
    user.deviceLanguage = req.body.deviceLanguage;
  }

  if (req.body.deviceId || req.body.webDeviceId) {
    if (req.body.deviceId) {
      user.deviceId = req.body.deviceId;
      user.appDeviceId = req.body.deviceId;
    }
    if (req.body.webDeviceId) {
      user.deviceId = req.body.webDeviceId;
      user.webDeviceId = req.body.webDeviceId;
    }
    if (user.deviceId && !user.deviceIdHistory.includes(user.deviceId)) {
      user.deviceIdHistory.push(user.deviceId);
    }
    if (!user.shadowBanned && !user.banned) {
      const isDeviceBanned = await BannedSource.findOne({ sourceType: 'deviceId', sourceVal: user.deviceId }).lean();
      if (isDeviceBanned) {
        await reportLib.autoShadowBan(user, 'loginSource already banned', `deviceId: ${user.deviceId}, user: ${isDeviceBanned.user}`);
      } else {
        const sameDeviceUsers = await User.find({
          _id: { $ne: user._id },
          deviceId: user.deviceId,
          bannedReason: { $nin: ['soft ban for unverified users', 'temp shadow ban due to inappropriate profile', 'Auto-ban: deviceId'] }, // for soft ban, temp shadow ban and deviceId ban skip banned device id logic
        });

        const bannedUser = sameDeviceUsers.find(u => u.shadowBanned);
        if (bannedUser) {
          for (let u of sameDeviceUsers.concat(user)) {
            await reportLib.autoShadowBan(u, 'deviceId', `deviceId: ${user.deviceId}, user: ${bannedUser._id?.toString()}`);
          }
        }
      }
    }
  }
  // Shadowban non-binary new scammers
  if (moment().diff(user.createdAt, 'days') < 3 && user.metrics.lastSeenWeb && user.gender === 'non-binary' && user.verification?.method === 'pose' && !user.shadowBanned) {
    await reportLib.shadowBan(user, null, 'non-binary AI pose', null);
  }
  if (req.body.timezone && momentTz.tz.zone(req.body.timezone) != null) {
    user.timezone = req.body.timezone;
    if (!user.signupCountry) {
      user.signupCountry = locationLib.getCountryNameFromTimezone(user.timezone);
    }
  }

  if (req.body.deviceSize) {
    const deviceSizes = ['s','m','l']
    const deviceSize = req.body.deviceSize.toLowerCase()
    if(deviceSizes.includes(deviceSize)){
      user.deviceSize = deviceSize
    }else{
      console.log(`user device size: ${deviceSize} not match with s/m/l format`)
    }
  }

  if (req.body.signupMethod) {
    user.signupMethod = req.body.signupMethod;
  }
}

function processLocale(req, user) {
  if (req.body.locale) {
    user.locale = req.body.locale;
  }
  if (user.locale == 'zh_Hans') {
    user.locale = 'zh-Hans';
  }
  if (user.locale == 'zh_Hant') {
    user.locale = 'zh-Hant';
  }
  if (req.body.countryLocale) {
    user.countryLocale = req.body.countryLocale;
  }
}

function processUtm(req, user) {
  if (req.body.utm_source && !user.utm_source) {
    const { utm_source } = req.body;
    if (typeof utm_source !== 'string' || utm_source.length > 4096) {
      return next(invalidInputError());
    }
    user.utm_source = utm_source;
  }
  if (req.body.utm_medium && !user.utm_medium) {
    const { utm_medium } = req.body;
    if (typeof utm_medium !== 'string' || utm_medium.length > 4096) {
      return next(invalidInputError());
    }
    user.utm_medium = utm_medium;
  }
  if (req.body.utm_campaign && !user.utm_campaign) {
    const { utm_campaign } = req.body;
    if (typeof utm_campaign !== 'string' || utm_campaign.length > 4096) {
      return next(invalidInputError());
    }
    user.utm_campaign = utm_campaign;
  }
  if (req.body.utm_content && !user.utm_content) {
    const { utm_content } = req.body;
    if (typeof utm_content !== 'string' || utm_content.length > 4096) {
      return next(invalidInputError());
    }
    user.utm_content = utm_content;
  }
  if (req.body.adset_name && !user.adset_name) {
    const { adset_name } = req.body;
    if (typeof adset_name !== 'string' || adset_name.length > 4096) {
      return next(invalidInputError());
    }
    user.adset_name = adset_name;
  }
  if (req.body.advertisingId && !user.advertisingId) {
    const { advertisingId } = req.body;
    if (typeof advertisingId !== 'string' || advertisingId.length > 4096) {
      return next(invalidInputError());
    }
    user.advertisingId = advertisingId;
  }
}

function resetTeleport(user) {
  user.teleportLocation = undefined;
  user.teleportCountryCode = undefined;
  user.teleportCountry = undefined;
  user.teleportState = undefined;
  user.teleportCity = undefined;

  if (user.actualLocation) {
    user.setLocation(user.actualLocation, user.actualCountryCode);
    user.country = user.actualCountry;
    user.state = user.actualState;
    user.city = user.actualCity;
  } else {
    user.setLocation(undefined, undefined);
    user.country = undefined;
    user.state = undefined;
    user.city = undefined;
  }

  if (user.locationOverride.city) {
    user.countryCode = user.locationOverride.countryCode;
    user.country = user.locationOverride.country;
    user.state = user.locationOverride.state;
    user.city = user.locationOverride.city;
  }

  user.calculateViewableInDailyProfiles();
}

async function deleteAccount(user) {
  if (user.admin) {
    console.log(`Not deleting user ${user._id} due to admin status`);
    return;
  }

  const uid = user._id;
  // console.log('Deleting user', uid, user);
  console.log('Deleting user', uid);

  admin.auth().deleteUser(uid)
    .then(() => { console.log(`deleted Firebase Account with uid: ${uid}`) })
    .catch((error) => { console.log(`error encountered while deleting firebase uid: ${uid} - ${error}`) })

  if (user.stripeCustomerId) {
    try {
      const deleted = await stripe.customers.del(user.stripeCustomerId);
      console.log('Deleted stripe customer', deleted);
    } catch (err) {
      console.log(err);
    }
  }

  await BannedUser.updateOne(
    { user: uid },
    { deletedAt: Date.now() },
  );

  await user.deleteAccount();
  console.log('Deleted user', uid);
}

async function backfillUserLatLong() {
  await User.updateMany(
    { location: { $exists: true } },
    [
      {
        $set: {
          countryGroup: { $cond: [ { $in: [ '$countryCode', countryLib.group1 ] }, 1, 0 ] },
          longitude: { $round: [ { $arrayElemAt: [ '$location.coordinates', 0 ] }, 1 ] },
          latitude: { $round: [ { $arrayElemAt: [ '$location.coordinates', 1 ] }, 1 ] },
          longitude2: { $divide: [ { $round: [ { $multiply: [ 50, { $arrayElemAt: [ '$location.coordinates', 0 ] } ] } ] }, 50 ] },
          latitude2: { $divide: [ { $round: [ { $multiply: [ 50, { $arrayElemAt: [ '$location.coordinates', 1 ] } ] } ] }, 50 ] },
          longitude3: { $divide: [ { $round: [ { $multiply: [ 0.4, { $arrayElemAt: [ '$location.coordinates', 0 ] } ] } ] }, 0.4 ] },
          latitude3: { $divide: [ { $round: [ { $multiply: [ 0.4, { $arrayElemAt: [ '$location.coordinates', 1 ] } ] } ] }, 0.4 ] },
        },
      },
    ],
  );
}

const allowedDevices = [
  '3B3EF5EC-D935-460F-AED4-0D3186502306',  // derek ios
  'd1210c2a-c0b8-4f7d-8f6b-a0a91b332db8',  // derek web
  'b76c228ab8d4c49f',                      // derek android
  '4B602BA9-696C-44E6-9A4C-7304FE8E0681',  // maya ios
  '7cdd33ed6e49521c',                      // maya android
  '031398BE-9DFF-4B09-A388-B0573C79BDEA',  // maya
  '5b9b5d954e2fe4d4',
  '7cdd33ed6e49521c',
  '535e33b6d5e4bdaa',
  '7721d517ce8e95b6',
  '576828050935951f',
  '37bf8af12b5f9906',                      // marlina android
  'DC837273-D324-4457-9F90-2E212D514CE1',  // marlina ios
  '5C9CD31D-4424-4F6F-A18D-E3A441A7C71B',  // marlina
];

async function isAuthAllowed(deviceId, email, ip, checkedBy) {
  if (process.env.NODE_ENV == 'beta') {
    return true;
  }
  if (allowedDevices.includes(deviceId)) {
    return true;
  }
  if (['<EMAIL>'].includes(email)) {
    return true;
  }
  if (email && isDisposable(email)) {
    await SignupRejected.create({
      checkedBy,
      email,
      deviceId,
      ip,
      reason: 'disposable email',
    });
    return false;
  }

  if (deviceId) {
    let users = await User.find({ deviceId }, 'email');
    if (users.length >= 5 && !users.find(x => x.email == email)) {
      await SignupRejected.create({
        checkedBy,
        email,
        deviceId,
        ip,
        reason: 'device id',
      });
      return false;
    }
  }

  if (ip) {
    let users = await User.find({
      'ipData.ip': ip,
      createdAt: { $gt: DateTime.utc().minus({ days: 1 }).toJSDate() },
    }, 'email');
    if (users.length >= 5) {
      let user = await User.findOne({
        email,
        'ipData.ip': ip,
      }, 'email');
      if (!user) {
        await SignupRejected.create({
          checkedBy,
          email,
          deviceId,
          ip,
          reason: 'ip address',
        });
        return false;
      }
    }
  }

  return true;
}

function useNewReverification(user) {
  if (!process.env.TESTING) {
    return true;
  }
  if (user.versionAtLeast('1.13.22')) {
    if (['en'].includes(user.locale)) {
      return true;
    }
  }
  if (user.versionAtLeast('1.13.24')) {
    if (['af','az','bg','ca','cs','da','de','es','fi','fil','fr','hi','hu','hy','id','it','ka','kk','ms','nl','pl','ro','sk','sr','sv','sw','te','tr','zh-Hant'].includes(user.locale)) {
      return true;
    }
  }
  if (user.versionAtLeast('1.13.25')) {
    if (['ar','bn','ja','ko','pt','ru','si','uk','ur','zh-Hans'].includes(user.locale)) {
      return true;
    }
  }
  if (user.versionAtLeast('1.13.27')) {
    if (['et'].includes(user.locale)) {
      return true;
    }
  }
  if (user.versionAtLeast('1.13.28')) {
    if (['mr','no'].includes(user.locale)) {
      return true;
    }
  }
  return false;
}

function limitLikes(user) {
  if (user.config && user.config.limit_likes) {
    return true;
  }
  if (user.config && user.config.limit_likes_2_days && moment().diff(user.createdAt, 'days') < 2) {
    return true;
  }
  return false;
}

function getRemainingDailyLimit(user) {
  const limit = constants.getKarmaTierSwipeLimits()[user.karmaTier];

  if (user.unlimitedLikesExpiration > Date.now()) {
    return limit;
  }
  if (user.metrics.swipeLimitResetTime <= Date.now()) {
    return limit;
  }

  let numActionsCurrentDay = user.metrics.numActionsCurrentDay;
  if (numActionsCurrentDay == user.currentDayMetrics.swipes.length) {
    numActionsCurrentDay = _.union(user.currentDayMetrics.swipes, user.currentDayMetrics.pendingSwipes).length;
  }

  const numActionsRemaining = limit + user.currentDayMetrics.numAdditionalSwipes - numActionsCurrentDay;
  const numLikesRemaining = constants.DAILY_LIKES_LIMIT + user.currentDayMetrics.numAdditionalSwipes - user.metrics.numLikesSentCurrentDay;

  if (limitLikes(user)) {
    return Math.max(numActionsRemaining, numLikesRemaining);
  }
  return numActionsRemaining;
}

function showSexualityPopup(user) {
  if (!user.versionAtLeast('1.13.59')) {
    return undefined;
  }

  const { sexuality, sexualityPopupClosed, createdAt, gender, preferences } = user;
  if (sexuality || sexualityPopupClosed) {
    return false;
  }

  const isExistingUser = moment().diff(createdAt, 'days') > 1;
  const isGenderMatch = preferences?.dating?.includes(gender) || preferences?.dating?.includes('non-binary');

  return isExistingUser && isGenderMatch;
}

async function handleAutomatedRejection(user, rejectionReason) {
  let notificationDelay = 10000;
  if (!user.events.finished_signup) {
    notificationDelay = 120000;
  }
  if (process.env.TESTING) {
    notificationDelay = 0;
  }

  await user.setVerificationStatus('rejected');
  user.verification.rejectionReason = rejectionReason;
  await user.save();

  if (!user.events.finished_signup && user.versionAtLeast('1.13.30')) {
    // notification will be sent on exitFlashSale event instead
    return;
  }

  // send delayed notifications
  setTimeout(notifyVerificationRejection, notificationDelay, user);
}

async function handleAutomatedApproval(user) {
  let notificationDelay = 10000;
  if (!user.events.finished_signup) {
    notificationDelay = 1000;
  }
  if (process.env.TESTING) {
    notificationDelay = 0;
  }
  let additionalDelayForCoinReward = 10000;
  if (process.env.TESTING) {
    additionalDelayForCoinReward = 0;
  }

  await user.setVerificationStatus('verified');
  user.verification.rejectionReason = undefined;
  await user.save();
  await updateUserScore(user, { verification: 1 });
  const data = {
    verificationStatus: user.verification.status,
    verified: user.verification.status === 'verified',
    rejectionReason: user.verification.rejectionReason,
  };
  const notifData = { verification: JSON.stringify(data) };

  // send delayed notifications
  setTimeout(async () => {
    sendSocketEvent(user._id, 'verification', data);
    sendNotification(
      user,
      null,
      translate('Verification Successful', user.locale),
      translate('Your profile has been verified.', user.locale),
      notifData,
      null,
      'general',
      'profile-verification-successful',
    );
    if (user.os === 'web') {
      await sendVerificationSuccessfulEmail(user);
    }
  }, notificationDelay);

  setTimeout(async () => {
    await grantVerifyProfileAward(user);
  }, notificationDelay + additionalDelayForCoinReward);
}

async function notifyVerificationRejection(user) {
  const data = {
    verificationStatus: user.verification.status,
    verified: user.verification.status === 'verified',
    rejectionReason: user.verification.rejectionReason,
  };
  if (data.rejectionReason == 'This version of the app is no longer supported. Please update to the latest version of Boo. Thank you!') {
    data.rejectionReason = translate(data.rejectionReason, user.locale);
  }
  const notifData = { verification: JSON.stringify(data) };
  sendSocketEvent(user._id, 'verification', data);
  let body = translate('Please follow the profile verification guidelines.', user.locale);
  if (user.verification.rejectionReason) {
    let reason = translate(user.verification.rejectionReason, user.locale);
    reason = reason.replace('.', '');
    body = translate('Reason: %s. Please follow the profile verification guidelines.', user.locale, reason);
  }
  sendNotification(
    user,
    null,
    translate('Verification Unsuccessful', user.locale),
    body,
    notifData,
    null,
    'negative',
    'profile-verification-unsuccessful',
  );
  if (user.os === 'web') {
    sendVerificationFailedEmail(user, body);
  }
}

async function updateVerificationStatus(user, status, reason, internalReason) {
  await user.setVerificationStatus(status, internalReason);
  user.verification.rejectionReason = reason;
  await user.save();
}

async function setAccountRestrictionsIfNeeded(user) {
  if(user.tempBanEndAt && user.tempBanEndAt < Date.now()){ // removing temp ban and temp ban appeal if time is already crossed
    await reportLib.undoTempBan(user, 'auto system temp ban end detected and unbanned');
  }
  if (countryLib.gdprCountries.includes(user.ipData?.country)) {
    if (!user.accountRestrictions) {
      user.accountRestrictions = {};
      await user.save()
    }
    if (user.profileTempBanReason && !user.accountRestrictions.profileTempBan) {
      user.accountRestrictions.profileTempBan = { appealStatus: 'allowed' };
      await user.save()
    }
  }
}

function getRemainingSaleTime(date, userLocale) {
  if (!date) return null;

  const now = moment();
  const endDate = moment(date);

  if (endDate.isBefore(now)) {
    return null;
  }

  const duration = moment.duration(endDate.diff(now));
  let hours = duration.hours();
  const minutes = duration.minutes();

  if (minutes >= 40) {
    hours += 1;
  }

  if (userLocale === 'en') {
    if (hours > 1) return `${hours} hours`;
    if (hours === 1) return '1 hour';
    return minutes === 1 ? '1 minute' : `${minutes} minutes`;
  }

  return hours >= 1
    ? translate_frontend('{}h', userLocale).replace('{}', hours)
    : translate_frontend('{}m', userLocale).replace('{}', minutes);
}

function calculateProfileCompletion(user) {
  if (!user) {
    return 0;
  }

  const isFilled = (field) => field && field.trim().length > 0;
  const hasItems = (array) => Array.isArray(array) && array.length > 0;

  let score = 4;
  const MAX_SCORE = 29;

  score += hasItems(user.pictures) ? user.pictures.length : 0;
  const prompts = user.originalFields?.prompts || user.prompts;
  score += hasItems(prompts) ? prompts.length : 0;

  score += isFilled(user.originalFields?.education) || isFilled(user.education) ? 1 : 0;
  score += isFilled(user.originalFields?.work) || isFilled(user.work) ? 1 : 0;
  score += ["verified", "reverifying"].includes(user.verification?.status) ? 1 : 0;
  score += hasItems(user.interestNames) ? 1 : 0;
  score += isFilled(user.originalFields?.description) || isFilled(user.description) ? 1 : 0;
  score += hasItems(user.ethnicities) ? 1 : 0;
  score += user.height != null ? 1 : 0;
  if (user.moreAboutUser) {
    Object.values(user.moreAboutUser).forEach(value => {
      if (value != null) {
        score += 1;
      }
    });
  }

  return score / MAX_SCORE;
}

async function handleEmbeddingUpdate(user, action, imageKey, previousKey = null) {
  const isImage = ['.jpg', '.jpeg', '.png'].some((ext) => imageKey.endsWith(ext));
  if (!user?.isVerified() || !user?.gender || new Date(user?.createdAt) < new Date('2023-03-25T00:00:00.000Z') || !isImage) {
    return;
  }

  const updateEmbeddings = async (pictureEmbeddings) => {
    if (pictureEmbeddings.length) {
      const mergedEmbedding = await mergeEmbeddings(pictureEmbeddings.map((entry) => entry.embedding));
      await UserEmbeddings.updateOne(
        { user: user._id },
        {
          pictureEmbeddings,
          merged_embedding: mergedEmbedding,
          latitude: user.latitude,
          longitude: user.longitude,
          countryCode: user.countryCode,
          hideFromVisionSearch: user.hideFromVisionSearch,
        },
      );
    } else {
      await UserEmbeddings.deleteOne({ user: user._id });
    }
  };

  if (action === 'delete') {
    const userEmbeddings = await UserEmbeddings.findOne({ user: user._id });
    if (!userEmbeddings) return;
    let pictureEmbeddings = userEmbeddings.pictureEmbeddings || [];
    pictureEmbeddings = pictureEmbeddings.filter((entry) => entry.key !== imageKey);
    await updateEmbeddings(pictureEmbeddings);
    return;
  }

  // To avoid race conditions, generate the embedding first
  const embedding = await generateEmbedding(constants.IMAGE_DOMAIN + imageKey);
  if (!embedding) return;

  const userEmbeddings = await UserEmbeddings.findOne({ user: user._id });
  let pictureEmbeddings = userEmbeddings?.pictureEmbeddings || [];

  if (action === 'edit' && previousKey) {
    pictureEmbeddings = pictureEmbeddings.filter((entry) => entry.key !== previousKey);
  }

  pictureEmbeddings.push({ key: imageKey, embedding });
  if (userEmbeddings) {
    await updateEmbeddings(pictureEmbeddings);
  } else {
    await UserEmbeddings.create({
      user: user._id,
      merged_embedding: embedding,
      latitude: user.latitude,
      longitude: user.longitude,
      pictureEmbeddings: [{ key: imageKey, embedding }],
      source: 'API', // Should be removed once backfill is complete
      hideFromVisionSearch: user.hideFromVisionSearch,
    });
  }
}

async function processProfileAnalysis (user) {
  const response = await openaiLib.getProfileAnalysis(user);
  await UsersProfileAnalysisPrompt.create({
    userId: user._id,
    prompt: JSON.stringify({ prompt: response.prompt || '' }, null, 2),
    images: response.images,
    output: JSON.stringify({ output: response.output || '' }, null, 2),
    outputResults: response.formattedOutputResults || '',
    promptTokens: response.promptTokens,
    outputTokens: response.outputTokens,
    isError: response.errorMessage ? true : false,
    errorMessage: response.errorMessage || undefined,
    cost: response.cost,
    model: response.model,
    processingTime: response.processingTime || 0,
  });
  return {
    outputResults: response.formattedOutputResults || {},
    isError: response.errorMessage ? true : false,
  };
}

async function handleAiFilterCleanup(filterId) {
  if (!filterId) return;

  const updatedFilter = await AiFilter.findByIdAndUpdate(
    filterId,
    { $inc: { usageCount: -1 } },
    { new: true },
  );
  // // Can remove this, if we want to keep all generated filters
  // if (updatedFilter?.usageCount <= 0) {
  //   await AiFilter.findByIdAndDelete(filterId);
  // }
}

async function handleAiFilterPreference(user, filterInput) {
  const previousFilterId = user.aiFilterPreference;
  const filter = filterInput?.toLowerCase();

  if (previousFilterId && user.aiFilter === filter) {
    return { _id: previousFilterId, filter };
  }

  const existingFilter = await AiFilter.findOneAndUpdate(
    { filter },
    { $inc: { usageCount: 1 } },
    { new: true },
  );

  if (existingFilter) {
    await handleAiFilterCleanup(previousFilterId);
    return existingFilter;
  }

  const { output: translatedFilter, cost: translationCost, errorMessage } = await getAifilterTranslation(filter);
  if (!translatedFilter) {
    console.log(`[Aifilter Error]: Failed to translate filter: ${filter} with error: ${errorMessage}`);
    return null;
  }

  if (!isTokenCountValidForClip(translatedFilter)) {
    console.log(`[Aifilter Error]: Filter: ${filter} translated to: ${translatedFilter} exceeds token limit for CLIP.`);
    return 'ERROR_TOO_LONG';
  }

  const existingTranslation = await AiFilter.findOne({ translatedFilter }).select('filterEmbedding').lean();
  let filterEmbedding = existingTranslation?.filterEmbedding;

  if (!filterEmbedding) {
    filterEmbedding = await generateEmbedding(null, translatedFilter);
    if (filterEmbedding === 'ERROR_TIMEOUT') return 'ERROR_TIMEOUT';
    if (!filterEmbedding) {
      console.log(`[Aifilter Error]: Failed to generate embedding for filter: ${filter} translated to: ${translatedFilter}`);
      return null;
    }
  }

  const newFilter = await AiFilter.create({
    createdBy: user._id,
    filter,
    translatedFilter,
    filterEmbedding,
    usageCount: 1,
    translationCost,
  });

  await handleAiFilterCleanup(previousFilterId);
  return newFilter;
}

async function getPopularAiFilters() {
  const popularFilters = await AiFilter.find()
    .sort({ usageCount: -1 })
    .select('_id filter usageCount')
    .limit(20)
    .lean();

  return popularFilters || [];
}

async function getLikedUnmatchedBlockedUserId(userId) {
  let ids = []
  let exclusionListDetail = await ExclusionListDetailed.findOne({ user : userId })
  if(exclusionListDetail?.likedList?.length) ids = ids.concat(exclusionListDetail.likedList)
  if(exclusionListDetail?.blockedList?.length) ids = ids.concat(exclusionListDetail.blockedList)
  return ids
}

const processUserAiTailoredPrompts = async (user, previousResponse) => {
  const response = await getAiTailoredPromptsForUser(user, previousResponse);
  await UsersAiTailoredPrompts.create({
      userId: user._id,
      prompt: JSON.stringify({ prompt: response.prompt || '' }, null, 2),
      output: JSON.stringify({ output: response.output || '' }, null, 2),
      outputPrompts: response.formattedOutputPrompts || '',
      promptTokens: response.promptTokens,
      outputTokens: response.outputTokens,
      isError: response.errorMessage ? true : false,
      errorMessage: response.errorMessage || undefined,
      cost: response.cost,
      model: response.model,
      processingTime: response.processingTime || 0,
    });
  return {
    outputPrompts: response.formattedOutputPrompts || [],
    isError: response.errorMessage ? true : false,
  };

};

/**
 * Converts `shadow_hide` action into a temporary shadow ban for GDPR users.
 *
 * Used when profile text and/or pictures were previously shadow-hidden, but should now be
 * handled with a temporary ban so the user can be notified of the violation and take corrective action.
 *
 * Process:
 * 1. Check if already restored
 *    - If the profile has already been checked and restored, exit early.
 *
 * 2. Restore hidden profile content
 *    - Restores `description`, `education`, `work`, `prompts`, and `pictures` from stored originals.
 *    - Clears `hiddenPictures` and `hiddenProfileText`.
 *    - Saves the updated user record.
 *
 * 3. Unban if reason was 'All pictures shadow hidden'
 *    - If the user is shadow banned for this reason, unban them first, then apply a temp ban and
 *      mark the pictures as infringing content.
 *
 * 4. Find most recent `shadow_hide` report
 *    - Searches `Report` first, then `PreemptiveModerationLog`, sorted by most recent.
 *
 * 5. Collect banned keywords
 *    - After restoring original content, runs `removeBannedKeywords` on `description`, `education`,
 *      `work`, and each prompt answer.
 *    - Merges detected keywords with any existing `infringingText` from the report.
 *    - Removes duplicates.
 *
 * 6. Apply a temporary ban
 *    - If a report exists and banned keywords are found:
 *        - Apply a temp ban with the combined infringing keywords and picture keys.
 *        - Mark the ban authority as `"openai"` so future profile updates are AI-reviewed first,
 *          then checked for banned keywords.
 *    - If no report exists but banned keywords are found:
 *        - Apply a temp ban with authority `"banned keyword system"`, so future updates check banned
 *          keywords first, then AI review.
 *
 * @async
 * @param {Object} user - User object containing profile data, moderation metadata, and original fields.
 */
async function convertShadowHideToTempShadowBan(user) {
  if (user.shadowBanned && !['All pictures shadow hidden', 'temp shadow ban due to inappropriate profile'].includes(user.bannedReason)) {
    // if the user is permanently banned, then no need to check
    return;
  }

  function restoreField(fieldName) {
    user[fieldName] = user.originalFields?.[fieldName] ?? user[fieldName];
  }

  function findInfringingTexts(infringingTexts) {
    if (!infringingTexts?.length) return [];

    let text = `${user.firstName || ''} ${user.originalFields.education || user.education || ''} ${user.originalFields.work || user.work || ''} ${user.originalFields.description || user.description || ''}`;

    const prompts = user.originalFields.prompts || user.prompts || [];
    for (let i = 0; i < prompts.length; i++) {
      text += ` ${prompts[i].answer || ''}`;
    }

    const lowerProfileText = text.toLowerCase();
    return infringingTexts.filter(t => lowerProfileText.includes(t.toLowerCase()));
  }

  function findInfringingPictures(reportId, openaiData, isFollowUp) {
    const { infringingPictureKeys, infringingPictures, prompt } = openaiData || {};

    try {
      let keys = [];

      if (Array.isArray(infringingPictureKeys) && infringingPictureKeys.length > 0) {
        keys = infringingPictureKeys;
      } else if (Array.isArray(infringingPictures) && !infringingPictureKeys) {
        const parsedPrompt = JSON.parse(prompt || '{}');
        keys = infringingPictures
          .map(index => parsedPrompt?.imageUrls?.[index]?.replace(constants.IMAGE_DOMAIN, ''))
          .filter(Boolean);
      }

      return keys.filter(key => user?.pictures?.includes(key));
    } catch (err) {
      console.log(`[ERROR] convertShadowHideToTempShadowBan: findInfringingPictures failed for report ${reportId}, isFollowUp=${isFollowUp}`, err);
      return [];
    }
  }

  // Fix for affected users, can remove later
  if (user.bannedReason == 'temp shadow ban due to inappropriate profile') {
    let shouldUnban = false;
    let report = await Report.findById(user.profileTempBanReportId) || await PreemptiveModerationLog.findById(user.profileTempBanReportId);
    if (report?.openaiFollowUp?.length > 0) {
      const lastFollowUp = report.openaiFollowUp[report.openaiFollowUp.length - 1];
      if (lastFollowUp.ban == false) {
        shouldUnban = true;
      }
    }

    if (findInfringingTexts(user.profileTempBanInfringingText).length === 0 &&
      (user.profileTempBanInfringingPictures || []).filter(pic => user.pictures?.includes(pic)).length === 0) {
      shouldUnban = true;
    }

    if (shouldUnban) {
      await reportLib.undoProfileTempBan(user);
      return;
    }
  }

  const isAlreadyRestored =
    !user.hiddenPictures &&
    !user.hiddenProfileText &&
    lodash.isEqual(user.description?.trim() || '', user.originalFields?.description?.trim() || '') &&
    lodash.isEqual(user.education?.trim() || '', user.originalFields?.education?.trim() || '') &&
    lodash.isEqual(user.work?.trim() || '', user.originalFields?.work?.trim() || '') &&
    lodash.isEqual(
      (user.prompts || []).map(item => ({
        id: item.id || '',
        prompt: item.prompt?.trim() || '',
        answer: item.answer?.trim() || '',
      })),
      (user.originalFields?.prompts || []).map(item => ({
        id: item.id || '',
        prompt: item.prompt?.trim() || '',
        answer: item.answer?.trim() || '',
      })),
    );

  if (isAlreadyRestored) return;

  // Restore original profile fields
  ['description', 'education', 'work', 'prompts'].forEach(restoreField);
  user.pictures = user.originalPictures ?? user.pictures;
  user.hiddenPictures = undefined;
  user.hiddenProfileText = undefined;
  await user.save();

  if (user.shadowBanned && user.bannedReason == 'All pictures shadow hidden') {
    await reportLib.unban(user, null, 'replace shadow_hide with temp_shadow_ban');
  }

  const query = {
    reportedUser: user._id,
    'openai.decision': 'shadow_hide',
  };

  let report = await Report.findOne(query).sort({ createdAt: -1 });
  if (!report) {
    report = await PreemptiveModerationLog.findOne(query).sort({ createdAt: -1 });
  }

  let infringingText = [];
  let infringingPictures = [];
  let shouldBanFromAI = false;

  if (report) {
    infringingText = report?.openai?.infringingText || [];
    infringingPictures = findInfringingPictures(report._id, report.openai);

    if (report.openaiFollowUp?.length > 0) {
      const lastFollowUp = report.openaiFollowUp[report.openaiFollowUp.length - 1];
      infringingText = lastFollowUp?.infringingText || [];
      infringingPictures = findInfringingPictures(report._id, lastFollowUp, true);
    }
    infringingText = findInfringingTexts(infringingText);

    if (infringingText.length || infringingPictures.length) {
      shouldBanFromAI = true;
    }
  }

  const keywords = new Set();
  const collectKeywords = (text) => {
    const { bannedKeywords } = removeBannedKeywords(text || '', true);
    bannedKeywords.forEach((kw) => keywords.add(kw));
  };

  collectKeywords(user.description);
  collectKeywords(user.education);
  collectKeywords(user.work);
  user.prompts?.forEach((p) => collectKeywords(p.answer));

  const bannedKeywords = [...new Set([...infringingText, ...keywords])];

  if (!shouldBanFromAI && !bannedKeywords.length) return;

  await reportLib.profileTempBan(
    user,
    shouldBanFromAI ? report?.openai?.banReason : 'banned keywords found in profile',
    shouldBanFromAI ? 'openai' : 'banned keyword system',
    shouldBanFromAI ? report?._id : null,
    bannedKeywords,
    infringingPictures || [],
    !!user.profileTempBanReason,
  );
}

async function updateLocationInEmbeddings(user) {
  await UserEmbeddings.updateOne(
    { user: user._id },
    { latitude: user.latitude, longitude: user.longitude, countryCode: user.countryCode },
  );
}

async function replaceShadowBanForInfringingName(user) {
  if (!user.shadowBanned) return;
  if (user.bannedReason !== 'infringing text found in name') return;
  await reportLib.unban(user, null, 'replace shadow_ban for infringing name with temp_shadow_ban');

  const query = {
    reportedUser: user._id,
    'openai.decision': 'shadow_hide',
    'openai.infringingTextFoundInName': true,
  };

  const [reports, preemptiveLogs] = await Promise.all([
    Report.find(query).sort({ createdAt: -1 }).lean(),
    PreemptiveModerationLog.find(query).sort({ createdAt: -1 }).lean(),
  ]);

  for (const entry of [...reports, ...preemptiveLogs]) {
    const infringingText = entry.openai.infringingText || [];
    if (infringingText.some(text => user.firstName.toLowerCase().includes(text.toLowerCase()))) {
      await reportLib.profileTempBan(
        user,
        entry.openai.banReason,
        'openai',
        entry._id,
        entry.openai.infringingText,
        entry.openai.infringingPictureKeys,
      );
      break;
    }
  }
}

/**
 * Detects banned keywords for GDPR-region users and applies or updates a temporary shadow ban if needed.
 *
 * For GDPR users, temporary shadow bans are used instead of hiding banned keywords from profiles.
 * This function is triggered during profile updates (bio, education, work, or prompt update)
 * with the latest text entered by the user.
 *
 * Process:
 * 1. Calls `removeBannedKeywords` to detect any banned keywords in `text`.
 * 2. If any are found:
 *    - Retrieves any existing infringing keywords from `user.profileTempBanInfringingText`.
 *    - Merges them with the newly detected keywords, removing duplicates.
 * 3. If there are no pre-existing infringing keywords, uses only the newly detected ones.
 * 4. Calls `reportLib.profileTempBan`:
 *    - If a temporary ban already exists, updates its infringing keyword list.
 *    - If no temporary ban exists, applies one with:
 *        - Reason: "banned keywords found in profile"
 *        - Authority: "banned keyword system"
 *        - Report ID: null
 *        - This ensures the system re-evaluates the profile using the banned keyword logic
 *          first on future profile updates.
 *
 * @async
 * @param {Object} user - The user object containing ban-related fields and metadata.
 * @param {string} text - The text to scan for banned keywords.
 */
async function detectAndTempBanForKeywords(user, text) {
  if (user.shadowBanned && !user.profileTempBanReason) {
    // if the user is permanently banned, then no need to check
    return;
  }

  const { bannedKeywords } = removeBannedKeywords(text || '', true);
  if (bannedKeywords.length > 0) {
    const existing = Array.isArray(user.profileTempBanInfringingText)
      ? user.profileTempBanInfringingText
      : [];

    const uniqueKeywords = [...new Set([...existing, ...bannedKeywords])];
    await reportLib.profileTempBan(
      user,
      user.profileTempBanReason || 'banned keywords found in profile',
      user.bannedBy || 'banned keyword system',
      user.profileTempBanReportId || null,
      uniqueKeywords,
      user.profileTempBanInfringingPictures || [],
      !!user.profileTempBanReason,
    );
  }
}

async function getUserAppCountryRanking(os,countryCode, { threshold = 5 } = {}){
    const cc = String(countryCode).trim().toUpperCase();
    const fieldPath = `topCombinedRanks.${cc}`;

    const doc = await StAppRankingSnapshot
      .findOne(
        { os, [fieldPath]: { $type: 'number', $gt: 0 } }, // ensure it's a positive number
        { createdAt: 1, [fieldPath]: 1 }                  // project only what we need
      )
      .sort({ createdAt: -1 })
      .lean();

    if (!doc) return { rank: null, createdAt: null };

    const raw = Number(doc.topCombinedRanks?.[cc]);
    const rank = Number.isFinite(raw) && raw > 0 && raw <= threshold ? raw : null;
    return { rank, createdAt: doc.createdAt ?? null };
}

module.exports = {
  formatMyProfile,
  processDeviceInfo,
  processUtm,
  processLocale,
  resetTeleport,
  deleteAccount,
  backfillUserLatLong,
  isAuthAllowed,
  useNewReverification,
  limitLikes,
  getRemainingDailyLimit,
  showSexualityPopup,
  handleAutomatedRejection,
  handleAutomatedApproval,
  notifyVerificationRejection,
  updateVerificationStatus,
  setAccountRestrictionsIfNeeded,
  getRemainingSaleTime,
  calculateProfileCompletion,
  handleEmbeddingUpdate,
  processProfileAnalysis,
  handleAiFilterCleanup,
  handleAiFilterPreference,
  getPopularAiFilters,
  getLikedUnmatchedBlockedUserId,
  processUserAiTailoredPrompts,
  convertShadowHideToTempShadowBan,
  updateLocationInEmbeddings,
  formatBanNotice,
  replaceShadowBanForInfringingName,
  detectAndTempBanForKeywords,
  getUserAppCountryRanking
};
