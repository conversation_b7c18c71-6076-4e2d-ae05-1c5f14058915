version: 1
cron:
 - name: "notifyFlashSaleExpiration"
   url: "/notifyFlashSaleExpiration"
   schedule: "* * * * *"
 - name: "deleteChats"
   url: "/deleteChats"
   schedule: "* * * * *"
 - name: "endBoostLiveActivity"
   url: "/endBoostLiveActivity"
   schedule: "* * * * *"
 - name: "dailyPush"
   url: "/dailyPush"
   schedule: "0 * * * *"
 - name: "sendEmailNotifications"
   url: "/sendEmailNotifications"
   schedule: "1 * * * *"
 - name: "secondSale"
   url: "/secondSale"
   schedule: "2 * * * *"
 - name: "inactiveReminder"
   url: "/inactiveReminder"
   schedule: "3 * * * *"
 - name: "dailyDigest"
   url: "/dailyDigest"
   schedule: "4 * * * *"
 - name: "updateQuestionScores"
   url: "/updateQuestionScores"
   schedule: "5 * * * *"
 - name: "updateUserScores"
   url: "/updateUserScores"
   schedule: "6 * * * *"
 - name: "deleteUsers"
   url: "/deleteUsers"
   schedule: "7 * * * *"
 - name: "cityNewUserPush"
   url: "/cityNewUserPush"
   schedule: "8 * * * *"
 - name: "superLikeSale"
   url: "/superLikeSale"
   schedule: "11 * * * *"
 - name: "coinsSale"
   url: "/coinsSale"
   schedule: "12 * * * *"
 - name: "recurringMonthlySale"
   url: "/recurringMonthlySale"
   schedule: "11 * 17 * *"
 - name: "birthdayPush"
   url: "/birthdayPush"
   schedule: "13 * * * *"
 - name: "replyToReviews"
   url: "/replyToReviews"
   schedule: "14 * * * *"
 - name: "day1SpiritRealmPush"
   url: "/day1SpiritRealmPush"
   schedule: "15 * * * *"
 - name: "day1UnlimitedLovesPush"
   url: "/day1UnlimitedLovesPush"
   schedule: "16 * * * *"
 - name: "day2Push"
   url: "/day2Push"
   schedule: "17 * * * *"
 - name: "day3Push"
   url: "/day3Push"
   schedule: "18 * * * *"
 - name: "day4Push"
   url: "/day4Push"
   schedule: "19 * * * *"
 - name: "day5Push"
   url: "/day5Push"
   schedule: "20 * * * *"
 - name: "boostPromoD1"
   url: "/boostPromoD1"
   schedule: "21 * * * *"
 - name: "boostSaleD3"
   url: "/boostSaleD3"
   schedule: "22 * * * *"
 - name: "grantCoinAwardsForReports"
   url: "/grantCoinAwardsForReports"
   schedule: "23 * * * *"
 - name: "updateSitemap"
   url: "/updateSitemap"
   schedule: "0 10 * * *"
 - name: "updateCountryFilterSitemaps"
   url: "/updateCountryFilterSitemaps"
   schedule: "0 8 * * 2"
 - name: "updateAutoIndexNowSitemap"
   url: "/updateAutoIndexNowSitemap"
   schedule: "0 8 * * 0"
 - name: "recordATT"
   url: "/recordATT"
   schedule: "52 23 * * *"
 - name: "voidInvoices"
   url: "/voidInvoices"
   schedule: "50 23 * * *"
 - name: "mark30DayInactive"
   url: "/mark30DayInactive"
   schedule: "53 23 * * *"
 - name: "backFillPersonalityData"
   url: "/backFillPersonalityData"
   schedule: "55 23 1 * *"
 - name: "recordInterestMetrics"
   url: "/recordInterestMetrics"
   schedule: "56 23 * * *"
 - name: "recordLanguageMetrics"
   url: "/recordLanguageMetrics"
   schedule: "0 0 1 * *"
 - name: "recordCityMetrics"
   url: "/recordCityMetrics"
   schedule: "58 23 * * *"
 - name: "recordMetrics"
   url: "/recordMetrics"
   schedule: "59 23 * * *"
 - name: "recordCohortCost"
   url: "/recordCohortCost"
   schedule: "0 7 * * *"
 - name: "exportData"
   url: "/exportData"
   schedule: "0 * * * *"
 - name: "setCacheImagesSocialProof"
   url: "/setCacheImagesSocialProof"
   schedule: "0 * * * *"
 - name: "exportChats"
   url: "/exportChats"
   schedule: "0 * * * *"
 - name: "processAIImageStaleBatches"
   url: "/processAIImageStaleBatches"
   schedule: "*/5 * * * *"
 - name: "cleanupDeletedUsers"
   url: "/cleanupDeletedUsers"
   schedule: "0 0 * * *"
 - name: "fetchAppRanking"
   url: "/fetchAppRanking"
   schedule: "0 3 * * *"

