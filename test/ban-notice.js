const { expect, assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const { app, validImagePath } = require('./common');
const User = require('../models/user');
const BanAppeal = require('../models/ban-appeal');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const TempBanAppeal = require('../models/temp-ban-appeal');
const reportLib = require('../lib/report');
const { setMockPromptResponse } = require('./stub');

describe('ban notice', async () => {
  beforeEach(async () => {
    // create admin user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();
  });

  it('eu user sees ban notice', async function () {

    // Panama
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    // not EU, so no ban notice
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // change to EU
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    // changing ip does not clear the ban notice
    // Panama
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    // unbanning should clear the ban notice
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
  });

  it('unmapped ban reasons should trigger ban notice with default ban reason spam', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Other' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });
  });

  it('undefined ban reason should trigger ban notice with default ban reason spam', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.shadowBanned = true;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });
  });

  it('country ban reasons should trigger ban notice with region unavailable', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ timezone: 'Africa/Lagos' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'Boo is currently not yet available in your region.',
      appealStatus: 'allowed',
    });

    // test backwards compatibility and translation
    user = await User.findById('0');
    user.banNotice.reason = 'spam';
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ja' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'Booはお住まいの地域ではまだご利用いただけません。',
      appealStatus: 'allowed',
    });

    user = await User.findById('0');
    expect(user.banNotice.reason).to.equal('Boo is currently not yet available in your region.');
  });

  it('multiple ban reasons', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // ban user multiple banned reasons
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({
        user: '0',
        bannedReasons: ['scamming', 'creating multiple accounts'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming, creating multiple accounts',
      reasons: [ 'scamming', 'creating multiple accounts' ],
      appealStatus: 'allowed',
    });
  });

  it('translated ban reason - single', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ja' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.shadowBanned = true;
    user.bannedReason = 'Other';
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'スパム',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ko' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: '스팸',
      appealStatus: 'allowed',
    });
  });

  it('translated ban reason - multiple', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ja' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.shadowBanned = true;
    user.bannedReason = 'Other';
    user.bannedReasons = ['scamming', 'creating multiple accounts'];
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: '詐欺行為、複数のアカウントを作成したこと',
      reasons: [ '詐欺行為', '複数のアカウントを作成したこと' ],
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ locale: 'ko' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: '사기 행위 및 여러 개의 계정을 만드는 행위',
      reasons: [ '사기 행위', '여러 개의 계정을 만드는 행위' ],
      appealStatus: 'allowed',
    });
  });

  it('give ban notice when free banned user tries to purchase', async function () {

    // Panama
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .get('/v1/user/isPurchaseAllowed')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.isPurchaseAllowed).to.equal(true);
    expect(res.body.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .get('/v1/user/isPurchaseAllowed')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.isPurchaseAllowed).to.equal(false);
    expect(res.body.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });
  });

  it('unverified ban reasons should not trigger ban notice', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'unverified with infringing text' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
  });

  it('temp shadow ban should not trigger ban notice', async function () {

    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // update/APP-878: initApp removes temp ban if no infringing text exists in user profile
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 0)
      .send({
        description: `description`,
      });
    expect(res.status).to.equal(200);

    // trigger temp shadow ban
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "infringingText": ["description"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: '0' });
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(res.body.user.banNotice).to.equal();
  });

  it('user with revenue sees ban notice even outside GDPR countries', async function () {

    // Panama (non-GDPR country)
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.metrics.revenue = 9.99; // User has made a purchase
    await user.save();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    // User with revenue should see ban notice even in non-GDPR country
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ appVersion: '1.13.89' });
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });
  });

  it('user with no revenue in non-GDPR country does not see ban notice', async function () {

    // Panama (non-GDPR country)
    ip = '**********';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ appVersion: '1.13.89' })
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal(null);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    // User with no revenue in non-GDPR country should not see ban notice
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({ appVersion: '1.13.89' });
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal(null);
  });
});


describe('appeals', async () => {
  let appealId;
  beforeEach(async () => {
    // create admin user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // create eu user with ban notice
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    // submit appeal
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'pending',
    });

    // get appeals
    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    console.log(res.body.appeals[0]);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not scammer');
    appealId = res.body.appeals[0]._id;

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.bannedReasons).to.eql(['Scammer']);
    expect(appeal.banNoticeReason).to.equal('scamming');
  });

  it('reject', async function () {
    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'rejected',
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('rejected');
    expect(appeal.notes).to.equal('is scammer');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not scammer' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('Scammer');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('appealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not scammer');
    expect(user.banHistory[2].action).to.equal('appealRejected');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal('is scammer');
  });

  it('approve', async function () {
    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not scammer');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not scammer' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('Scammer');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('appealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not scammer');
    expect(user.banHistory[2].action).to.equal('appealApproved');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal('is not scammer');
    expect(user.banHistory[3].action).to.equal('unban');
    expect(user.banHistory[3].by).to.equal('1');
    expect(user.banHistory[3].reason).to.equal();
    expect(user.banHistory[3].notes).to.equal('is not scammer (appeal approved)');
  });

  it('one appeal allowed per ban', async function () {
    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not scammer');

    // ban again
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Spam' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });

    // can appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'not spam' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    console.log(res.body.appeals[0]);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not spam');
    appealId = res.body.appeals[0]._id;
  });

  it('dismiss pending appeal if user unbanned before appeal reviewed', async function () {
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    appeals = await BanAppeal.find();
    expect(appeals.length).to.equal(1);
    appeal = appeals[0];
    expect(appeal.reviewedBy).to.equal();
    expect(appeal.reviewedAt).to.equal();
    expect(appeal.decision).to.equal('dismissed');
    expect(appeal.notes).to.equal('user was unbanned before appeal was reviewed');

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('Scammer');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('appealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not scammer');
    expect(user.banHistory[2].action).to.equal('unban');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal();
    expect(user.banHistory[3].action).to.equal('appealDismissed');
    expect(user.banHistory[3].by).to.equal();
    expect(user.banHistory[3].reason).to.equal();
    expect(user.banHistory[3].notes).to.equal('user was unbanned before appeal was reviewed');
  });

  it('should reject appeals if another account on the same device has a rejected appeal', async function () {
    // add device id
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ deviceId: 'device0' });
    expect(res.status).to.equal(200);

    // create new account with the same device id and submit ban appeal - should not be auto rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .set('X-Forwarded-For', ip)
      .send({ deviceId: 'device0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 3)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'pending',
    });

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(2);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not scammer');
    expect(res.body.appeals[1].user).to.equal('3');
    expect(res.body.appeals[1].comment).to.equal('not scammer');
    appealIdUser0 = res.body.appeals[0]._id;
    appealIdUser3 = res.body.appeals[1]._id;

    // now reject appeals
    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId: appealIdUser3, decision: 'rejected', notes: 'is scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId: appealIdUser0, decision: 'rejected', notes: 'is scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    // create new account with the same device id and submit ban appeal - should be auto rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', ip)
      .send({ deviceId: 'device0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 2)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    // user should see appeal pending
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'pending',
    });

    // admin should not see pending appeal
    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    // check raw data
    appeal = await BanAppeal.findOne({}).sort('-createdAt');
    expect(appeal.user).to.equal('2');
    expect(appeal.decision).to.equal('rejected');
    expect(appeal.reviewedBy).to.equal();
    expect(appeal.reviewedAt).to.equal();

    user = await User.findById('2');
    expect(user.banNotice.appealStatus).to.equal('rejected');
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[2].action).to.equal('appealRejected');
    expect(user.banHistory[2].by).to.equal(null);

    // Fast forward 6 hours
    let clock = sinon.useFakeTimers({
      now: Date.now() + 6 * 60 * 60 * 1000,
    });

    // now user should see appeal rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'rejected',
    });

    clock.restore();

    // create new account with different device id and submit ban appeal - should not be auto rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .set('X-Forwarded-For', ip)
      .send({ deviceId: 'device4' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '4', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 4)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'pending',
    });

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('4');
    expect(res.body.appeals[0].comment).to.equal('not scammer');
  });

  it('test mass unban when ban appeal approved', async () => {
    let res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    user = await User.findById('0');
    user.deviceId = 'device1';
    await user.save();
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();

    appeal = await BanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not scammer');

    //making 11 profile a profile temp ban
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 11)
      .send({ deviceId: 'device1' });
    expect(res.status).to.equal(200);

    // update/APP-878: initApp removes temp ban if no infringing text exists in user profile
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 11)
      .send({
        description: `nude text`,
      });
    expect(res.status).to.equal(200);

    setMockPromptResponse(`{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text"], "infringingPictures": [0]}`);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '11',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 11)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(res.body.user.banNotice).to.eql();

    for (let i = 2; i <= 10; i++) {
      // create more users on the same device
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ deviceId: 'device1' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/admin/ban')
        .set('authorization', 1)
        .send({ user: `${i}`, bannedReason: 'Spam' });
      expect(res.status).to.equal(200);
    }

    // ban again
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Spam' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'spam',
      appealStatus: 'allowed',
    });

    // can appeal again
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'not spam' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    console.log(res.body.appeals[0]);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not spam');
    appealId = res.body.appeals[0]._id;

    res = await request(app)
      .put('/v1/admin/banAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not spam' });
    expect(res.status).to.equal(200);

    await new Promise(resolve => setTimeout(resolve, 200));

    for (let i = 2; i <= 10; i++) {
      let user = await User.findById(`${i}`);
      expect(user.shadowBanned).to.equal(false);
      expect(user.bannedReason).to.equal();
      expect(user.bannedNotes).to.equal();
      expect(user.banHistory[user.banHistory.length - 1].notes).to.equal(`is not spam (appeal approved) (mass unban with device ID: device1)`);
    }

    // profileTempBan profile should not be unbanned
    user = await User.findById('11').lean()
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');

  });

  it('split ban appeal queue according to admin permissions and ban reasons', async () => {
    // create a second admin user with adminPermissions reviewBanAppeal
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    user.admin = true;
    user.adminPermissions = { support: true, reviewBanAppeal: true };
    await user.save();

    // set admin 1 permissions to manager
    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { support: true, manager: true };
    await user.save();

    // create another eu user with ban reason impersonation
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 2)
      .send({ user: '3', bannedReason: 'impersonation' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'impersonation',
      appealStatus: 'allowed',
    });

    // submit appeal
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 3)
      .send({ comment: 'not impersonation' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'impersonation',
      appealStatus: 'pending',
    });

    /*
    // admin 2 should only see the appeal for impersonation
    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('3');
    expect(res.body.appeals[0].comment).to.equal('not impersonation');
    */

    // admin 1 should see both appeals if queue is not specified
    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(2);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not scammer');
    expect(res.body.appeals[1].user).to.equal('3');
    expect(res.body.appeals[1].comment).to.equal('not impersonation');

    // if queue is 0, admin 1 should only see the appeal for scamming
    res = await request(app)
      .get('/v1/admin/banAppeal?queue=0')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not scammer');

    // if queue is 1, admin 1 should only see the appeal for impersonation
    res = await request(app)
      .get('/v1/admin/banAppeal?queue=1')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('3');
    expect(res.body.appeals[0].comment).to.equal('not impersonation');

    // admin 2 should see the same behavior as admin 1
    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(2);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not scammer');
    expect(res.body.appeals[1].user).to.equal('3');
    expect(res.body.appeals[1].comment).to.equal('not impersonation');

    res = await request(app)
      .get('/v1/admin/banAppeal?queue=0')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not scammer');

    res = await request(app)
      .get('/v1/admin/banAppeal?queue=1')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('3');
    expect(res.body.appeals[0].comment).to.equal('not impersonation');
  });

  it('should auto reject appeals if another account on the same device has underage ban', async () => {
    // add device id
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ deviceId: 'device0' });
    expect(res.status).to.equal(200);

    // create an account with underage ban
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    // add underage birthday
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 3)
      .send({
        year: new Date().getFullYear() - 16,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    let user = await User.findById('3');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('underage birthday');

    // create new account with the same device id as user 0 and submit ban appeal - should not be auto rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .set('X-Forwarded-For', ip)
      .send({ deviceId: 'device0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 4)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'pending',
    });

    // add another user with the same device id
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 5)
      .set('X-Forwarded-For', ip)
      .send({ deviceId: 'device0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 5)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'allowed',
    });

    // add same device id to user 3: underage ban
    user = await User.findById('3');
    user.deviceId = 'device0';
    await user.save();

    // new appeal should be auto rejected
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 5)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    // user should see appeal pending
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 5)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'pending',
    });

    // admin should see pending appeal for user 5
    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(2);
    for (let appeal of res.body.appeals) {
      expect(appeal.user).to.be.oneOf(['0', '4']);
      expect(appeal.comment).to.equal('not scammer');
    }

    // check raw data
    let appeal = await BanAppeal.findOne({}).sort('-createdAt');
    expect(appeal.user).to.equal('5');
    expect(appeal.decision).to.equal('rejected');
    expect(appeal.reviewedBy).to.equal();
    expect(appeal.reviewedAt).to.equal();

    user = await User.findById('5');
    expect(user.banNotice.appealStatus).to.equal('rejected');
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[2].action).to.equal('appealRejected');
    expect(user.banHistory[2].by).to.equal(null);

    // Fast forward 6 hours
    let clock = sinon.useFakeTimers({
      now: Date.now() + 6 * 60 * 60 * 1000,
    });

    // now user should see appeal rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 5)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'creating multiple accounts',
      appealStatus: 'rejected',
    });

    clock.restore();

    // create new account with different device id and submit ban appeal - should not be auto rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 6)
      .set('X-Forwarded-For', ip)
      .send({ deviceId: 'device4' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '6', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 6)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 6)
      .send({ comment: 'not scammer' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 6)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'pending',
    });

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(3);
    expect(res.body.appeals[2].user).to.equal('6');
    expect(res.body.appeals[2].comment).to.equal('not scammer');
  });
});

describe('underage birthday ban handling', async () => {

  it('should automatically ban user under 18 and unban user when they turn 18', async function () {
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    const seventeenYearsAgo = new Date();
    seventeenYearsAgo.setFullYear(seventeenYearsAgo.getFullYear() - 17);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: seventeenYearsAgo.getFullYear(),
        month: seventeenYearsAgo.getMonth() + 1,
        day: seventeenYearsAgo.getDate(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.birthday).to.exist;
    expect(res.body.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // Verify they are banned
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('underage birthday');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // Fast forward time to next month
    let clock = sinon.useFakeTimers({
      now: Date.now() + 30 * 24 * 60 * 60 * 1000,
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // Fast forward time to next year
    clock = sinon.useFakeTimers({
      now: Date.now() + 365 * 24 * 60 * 60 * 1000,
    });

    // Call initApp which should trigger the unban logic
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();

    // Verify user is no longer banned
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
    expect(user.banNotice).to.equal();

    clock.restore();
  });

  it('should not unban users banned for other reasons when they turn 18', async function () {
    // Lithuania (EU country to see ban notice)
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({ user: '0', bannedReason: 'Scammer' });
    expect(res.status).to.equal(200);

    const eighteenYearsAgo = new Date();
    eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - 18);

    user = await User.findById('0');
    user.birthday = eighteenYearsAgo;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'scamming',
      appealStatus: 'allowed',
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
  });

  it('should handle age calculation with user timezone correctly', async function () {

    let clock = sinon.useFakeTimers({ now: new Date("2025-07-17T01:00:00Z").getTime() });

    // Initialize user
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ timezone: 'America/Los_Angeles' })
    expect(res.status).to.equal(200);

    let user = await User.findById('0');
    expect(user.timezone).to.equal('America/Los_Angeles');

    const birthday = new Date('2007-07-17')

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: birthday.getFullYear(),
        month: birthday.getMonth() + 1,
        day: birthday.getDate(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.birthday).to.exist;
    expect(res.body.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('underage birthday');
    expect(user.age).to.equal(17);

    // now time zone in Asia/Singapore will be ahead than UTC time
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ timezone: 'Asia/Singapore' })
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.timezone).to.equal('Asia/Singapore');

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: birthday.getFullYear(),
        month: birthday.getMonth() + 1,
        day: birthday.getDate(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.birthday).to.exist;

    user = await User.findById('1');
    expect(user.shadowBanned).to.equal(false);
    expect(user.age).to.equal(18);

    clock.restore();
  });

  it('should reject appeals for underage birthday', async function () {
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    const seventeenYearsAgo = new Date();
    seventeenYearsAgo.setFullYear(seventeenYearsAgo.getFullYear() - 17);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: seventeenYearsAgo.getFullYear(),
        month: seventeenYearsAgo.getMonth() + 1,
        day: seventeenYearsAgo.getDate(),
      });
    expect(res.status).to.equal(200);
    expect(res.body.birthday).to.exist;
    expect(res.body.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // Verify they are banned
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('underage birthday');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'allowed',
    });

    // submit appeal
    res = await request(app)
      .put('/v1/user/banAppeal')
      .set('authorization', 0)
      .send({ comment: 'ok' })
    expect(res.status).to.equal(200);

    // user should see appeal pending
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'pending',
    });

    // admin should not see pending appeal
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .get('/v1/admin/banAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    // check raw data
    appeals = await BanAppeal.find({});
    expect(appeals.length).to.equal(1);
    expect(appeals[0].decision).to.equal('rejected');
    expect(appeals[0].reviewedBy).to.equal();
    expect(appeals[0].reviewedAt).to.equal();

    user = await User.findById('0');
    expect(user.banNotice.appealStatus).to.equal('rejected');
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[2].action).to.equal('appealRejected');
    expect(user.banHistory[2].by).to.equal(null);

    // Fast forward 6 hours
    let clock = sinon.useFakeTimers({
      now: Date.now() + 6 * 60 * 60 * 1000,
    });

    // now user should see appeal rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql({
      reason: 'underage',
      appealStatus: 'rejected',
    });

    clock.restore();
  });
});

it('should reject appeals for geo auto bans', async function () {
  // Lithuania
  ip = '***************';

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .set('X-Forwarded-For', ip)
    .send({ timezone: 'Africa/Lagos' })
  expect(res.status).to.equal(200);

  // Verify they are banned
  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('Auto-ban: Nigeria');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .set('X-Forwarded-For', ip)
  expect(res.status).to.equal(200);
  expect(res.body.user.banNotice).to.eql({
    reason: 'Boo is currently not yet available in your region.',
    appealStatus: 'allowed',
  });

  // submit appeal
  res = await request(app)
    .put('/v1/user/banAppeal')
    .set('authorization', 0)
    .send({ comment: 'ok' })
  expect(res.status).to.equal(200);

  // user should see appeal pending
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .set('X-Forwarded-For', ip)
  expect(res.status).to.equal(200);
  expect(res.body.user.banNotice).to.eql({
    reason: 'Boo is currently not yet available in your region.',
    appealStatus: 'pending',
  });

  // admin should not see pending appeal
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  res = await request(app)
    .get('/v1/admin/banAppeal')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.appeals.length).to.equal(0);

  // check raw data
  appeals = await BanAppeal.find({});
  expect(appeals.length).to.equal(1);
  expect(appeals[0].decision).to.equal('rejected');
  expect(appeals[0].reviewedBy).to.equal();
  expect(appeals[0].reviewedAt).to.equal();

  user = await User.findById('0');
  expect(user.banNotice.appealStatus).to.equal('rejected');
  expect(user.banHistory.length).to.equal(3);
  expect(user.banHistory[2].action).to.equal('appealRejected');
  expect(user.banHistory[2].by).to.equal(null);

  // Fast forward 6 hours
  let clock = sinon.useFakeTimers({
    now: Date.now() + 6 * 60 * 60 * 1000,
  });

  // now user should see appeal rejected
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .set('X-Forwarded-For', ip)
  expect(res.status).to.equal(200);
  expect(res.body.user.banNotice).to.eql({
    reason: 'Boo is currently not yet available in your region.',
    appealStatus: 'rejected',
  });

  clock.restore();
});

describe('profile temp ban appeals', async () => {
  let appealId, pictureKey, pictureKey2;
  let user, res, ip, appeal;
  beforeEach(async () => {
    // create admin user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // create eu user with profile temp ban
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    // upload picture
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // upload picture
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    pictureKey = user.pictures[0];
    pictureKey2 = user.pictures[1];
    
    // condition setup
    user.description = 'nude text';
    await user.save();

   

    // First report to start the process
    setMockPromptResponse('{"ban": false}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    // trigger profile temp ban
    setMockPromptResponse(`{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text"], "infringingPictures": [0]}`);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });

    user = await User.findById('0').lean()
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingText).to.eql(['nude text']);
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey]);
    expect(user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });

    // submit temp ban appeal
    res = await request(app)
      .put('/v1/user/profileTempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'pending',
      },
    });

    // get temp ban appeals
    res = await request(app)
      .get('/v1/admin/profileTempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not inappropriate');
    appealId = res.body.appeals[0]._id;

    appeal = await ProfileTempBanAppeal.findById(appealId);
    expect(appeal.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(appeal.profileTempBanInfringingText).to.eql(['nude text']);
    expect(appeal.profileTempBanInfringingPictures).to.eql([pictureKey]);

    user = await User.findById('0').lean()
    expect(user.banHistory.length).to.equal(2);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[1].action).to.equal('profileTempBanAppealSubmitted');

    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'pending',
      },
    });
    expect(user.profileTempBanInfringingText).to.eql(['nude text']);
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey]);
  });

  it('reject temp ban appeal', async function () {

    let tempBanAppealData = await ProfileTempBanAppeal.find({ user: '0' });
    expect(tempBanAppealData.length).to.equal(1);
    expect(tempBanAppealData[0].decision).to.equal();

    let user = await User.findById('0').lean()
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'pending',
      },
    });

    res = await request(app)
      .put('/v1/admin/profileTempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is truly inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/profileTempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    tempBanAppealData = await ProfileTempBanAppeal.find({ user: '0' });
    expect(tempBanAppealData.length).to.equal(1);
    expect(tempBanAppealData[0].decision).to.equal('rejected');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'rejected',
      },
    });

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');

    appeal = await ProfileTempBanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('rejected');
    expect(appeal.notes).to.equal('is truly inappropriate');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/profileTempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/profileTempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('openai');
    expect(user.banHistory[1].action).to.equal('profileTempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('profileTempBanAppealRejected');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].notes).to.equal('is truly inappropriate');
  });

  it('approve temp ban appeal', async function () {
    user = await User.findById('0');
    const originalInfringingText = user.profileTempBanInfringingText;
    const originalInfringingPictures = user.profileTempBanInfringingPictures;

    res = await request(app)
      .put('/v1/admin/profileTempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/profileTempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
    expect(res.body.user.accountRestrictions).to.eql({});

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();
    expect(user.appealedProfileTempBanInfringingText).to.eql(originalInfringingText);
    expect(user.appealedProfileTempBanInfringingPictures).to.eql(originalInfringingPictures);

    appeal = await ProfileTempBanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not inappropriate');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/profileTempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/profileTempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('openai');
    expect(user.banHistory[1].action).to.equal('profileTempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('profileTempBanAppealApproved');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].notes).to.equal('is not inappropriate');
    expect(user.banHistory[3].action).to.equal('unban');
    expect(user.banHistory[3].notes).to.equal('undo temp shadow ban due to inappropriate profile');
  });

  it('prevent re-banning for same appealed content', async function () {
    res = await request(app)
      .put('/v1/admin/profileTempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.appealedProfileTempBanInfringingText).to.eql(['nude text']);

    // Try to trigger profile temp ban again with same infringing text
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text content"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    // condition for new ban met with different text
    user.description = 'different inappropriate text';
    await user.save();

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["different inappropriate text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingText).to.eql(['different inappropriate text']);
  });

  it('prevent re-banning for same appealed pictures', async function () {
    res = await request(app)
      .put('/v1/admin/profileTempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);

    setMockPromptResponse(`{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingPictures": [0]}`);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();

    setMockPromptResponse(`{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingPictures": [1]}`);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey2]);
    expect(user.appealedProfileTempBanInfringingText).to.eql(['nude text']);
    expect(user.appealedProfileTempBanInfringingPictures).to.eql([pictureKey]);
  });

  it('mixed content filtering - partial match', async function () {
    res = await request(app)
      .put('/v1/admin/profileTempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    
    // condition setup for new ban with mix of appealed and new content
    user.work = 'new bad text';
    await user.save();

    // Try to trigger profile temp ban with mix of appealed and new content
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text", "new bad text"], "infringingPictures": [1]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.profileTempBanInfringingText).to.eql(['new bad text']);
    expect(user.profileTempBanInfringingPictures).to.eql([pictureKey2]);

    //init app
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/profileTempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'not inappropriate ban' })
    expect(res.status).to.equal(200);

    // Get the new appeal ID
    res = await request(app)
      .get('/v1/admin/profileTempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    const newAppealId = res.body.appeals[0]._id;

    res = await request(app)
      .put('/v1/admin/profileTempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId: newAppealId, decision: 'approved', notes: 'is not inappropriate second time' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.appealedProfileTempBanInfringingText).to.eql(['nude text', 'new bad text']);
    expect(user.appealedProfileTempBanInfringingPictures).to.eql([pictureKey, pictureKey2]);
  });

  it('appeal status reset for new ban after rejected appeal', async function () {
    res = await request(app)
      .put('/v1/admin/profileTempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is truly inappropriate' })
    expect(res.status).to.equal(200);

    let user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
    expect(user.accountRestrictions.profileTempBan.appealStatus).to.equal('rejected');

    await reportLib.undoProfileTempBan(user);
    user.work = 'harassment text'; // update/APP-878: initApp removes temp ban if no infringing text exists in user profile
    await user.save();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanReason).to.equal();
    expect(user.accountRestrictions.profileTempBan).to.equal();

    setMockPromptResponse('{"ban": true, "reason": "Harassment", "violationLocation": "profile", "infringingText": ["harassment text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'New inappropriate content',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanReason).to.equal('Harassment');
    expect(user.profileTempBanInfringingText).to.eql(['harassment text']);
    expect(user.accountRestrictions.profileTempBan).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', '***************')
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.profileTempBan.appealStatus).to.equal('allowed');

    // Verify user can now appeal the new ban
    res = await request(app)
      .put('/v1/user/profileTempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'this new ban is not appropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.accountRestrictions.profileTempBan.appealStatus).to.equal('pending');

    const appeals = await ProfileTempBanAppeal.find({ user: '0' }).sort('createdAt');
    expect(appeals.length).to.equal(2); // Original appeal + new appeal
    expect(appeals[1].profileTempBanReason).to.equal('Harassment');
    expect(appeals[1].profileTempBanInfringingText).to.eql(['harassment text']);
    expect(appeals[1].comment).to.equal('this new ban is not appropriate');
  });

  it('account restrictions initialization for GDPR users', async function () {
    // Test user without accountRestrictions in GDPR country
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '***************') // Lithuania
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql({});

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body.accountRestrictions).to.eql({});

    let user = await User.findById('2').lean()
    expect(user.accountRestrictions).to.eql({});

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 2)
      .set('X-Forwarded-For', '***************')
    expect(res.status).to.equal(200);
    expect(res.body.accountRestrictions).to.eql({});

    // update/APP-878: initApp removes temp ban if no infringing text exists in user profile
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 2)
      .send({
        description: `nude text`,
      });
    expect(res.status).to.equal(200);

    // Trigger profile temp ban
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '2',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '***************') // Lithuania
    expect(res.status).to.equal(200);

    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .set('X-Forwarded-For', '***************') // Lithuania
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('X-Forwarded-For', '***************') // Lithuania
      .set('authorization', 3)
    expect(res.status).to.equal(200);
    expect(res.body.accountRestrictions).to.eql({});

    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 3)
      .send({
        description: `nude text`,
      });
    expect(res.status).to.equal(200);

    // Trigger profile temp ban
    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["nude text"]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '3',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: '3' })
    expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('X-Forwarded-For', '***************') // Lithuania
      .set('authorization', 3)
    expect(res.status).to.equal(200);
    expect(res.body.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .set('X-Forwarded-For', '***************') // Lithuania
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql({
      profileTempBan: {
        appealStatus: 'allowed',
      },
    })
  });

  it('account restrictions initialization for non GDPR users', async function () {
    // Test user without accountRestrictions in non GDPR country
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '**********') // Panama
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql();

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({});

    setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile"}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '2',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '**********') // Panama
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql();

    res = await request(app)
      .put('/v1/user/profileTempBanAppeal')
      .set('authorization', 2)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(403);

    user = await User.findById('2');
    user.metrics.revenue = 1
    await user.save()

    res = await request(app)
      .put('/v1/user/profileTempBanAppeal')
      .set('authorization', 2)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({});

  });

  it('dismiss pending appeal if user unbanned before appeal reviewed', async function () {
    let user = await User.findOne({_id: '0'})
    await reportLib.undoProfileTempBan(user)

    res = await request(app)
      .get('/v1/admin/profileTempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.profileTempBan).to.equal();

    appeals = await ProfileTempBanAppeal.find();
    expect(appeals.length).to.equal(1);
    appeal = appeals[0];
    expect(appeal.reviewedBy).to.equal();
    expect(appeal.reviewedAt).to.equal();
    expect(appeal.decision).to.equal('dismissed');
    expect(appeal.notes).to.equal('user was unbanned before appeal was reviewed');

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('openai');
    expect(user.banHistory[0].reason).to.equal('temp shadow ban due to inappropriate profile');
    expect(user.banHistory[0].notes).to.equal('Nudity/Sexual Content - keyword: nude text');
    expect(user.banHistory[1].action).to.equal('profileTempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('unban');
    expect(user.banHistory[2].by).to.equal(null);
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal('undo temp shadow ban due to inappropriate profile');
    expect(user.banHistory[3].action).to.equal('ProfileTempBanAppealDismissed');
    expect(user.banHistory[3].by).to.equal();
    expect(user.banHistory[3].reason).to.equal();
    expect(user.banHistory[3].notes).to.equal('user was unbanned before appeal was reviewed');
  });
});

describe('temp ban appeals', async () => {
  let appealId;
  let user, res, ip, appeal;
  beforeEach(async () => {
    // create admin user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    // create eu user with temp ban
    // Lithuania
    ip = '***************';

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
      .send({
        appVersion: '1.11.46'
      })
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);
    q2Id = res.body._id;

    res = await request(app)
      .put('/v1/admin/tempBan')
      .set('authorization', 1)
      .send({ reason: 'text', banUser: '0', infringingPost: q2Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', ip)
    expect(res.status).to.equal(200);
    expect(res.body.user.tempBanReason).to.equal('text');
    expect(res.body.user.tempBanEndAt).to.not.eql(undefined);
    expect(res.body.user.accountRestrictions.tempBan.appealStatus).to.equal('allowed');

    user = await User.findById('0').lean()
    expect(user.tempBanReason).to.equal('text');
    expect(user.tempBanEndAt).to.not.eql(undefined);
    expect(user.accountRestrictions.tempBan.appealStatus).to.equal('allowed');

    // submit temp ban appeal
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions.tempBan.appealStatus).to.equal('pending');
    expect(res.body.user.accountRestrictions.tempBan.evidence.post.title).to.eql('first post');

    // get temp ban appeals
    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(1);
    expect(res.body.appeals[0].user).to.equal('0');
    expect(res.body.appeals[0].comment).to.equal('not inappropriate');
    appealId = res.body.appeals[0]._id;

    appeal = await TempBanAppeal.findById(appealId);
    expect(appeal.bannedReasons).to.eql(['text']);

    user = await User.findById('0').lean()
    expect(user.banHistory.length).to.equal(2);
    expect(user.banHistory[0].action).to.equal('tempBan');
    expect(user.banHistory[1].action).to.equal('tempBanAppealSubmitted');

    expect(user.tempBanReason).to.equal('text');
    expect(user.tempBanEndAt).to.not.eql(undefined);
    expect(user.accountRestrictions.tempBan.appealStatus).to.equal('pending');
  });

  it('reject temp ban appeal', async function () {

    let tempBanAppealData = await TempBanAppeal.find({ user: '0' });
    expect(tempBanAppealData.length).to.equal(1);
    expect(tempBanAppealData[0].decision).to.equal();

    let user = await User.findById('0').lean()
    expect(user.tempBanReason).to.equal('text');
    expect(user.tempBanEndAt).to.not.eql(undefined);
    expect(user.accountRestrictions.tempBan.appealStatus).to.equal('pending');

    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is truly inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    tempBanAppealData = await TempBanAppeal.find({ user: '0' });
    expect(tempBanAppealData.length).to.equal(1);
    expect(tempBanAppealData[0].decision).to.equal('rejected');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.eql();
    expect(res.body.user.accountRestrictions.tempBan.appealStatus).to.equal('rejected');

    user = await User.findById('0');
    expect(user.tempBanEndAt).to.not.eql(undefined);
    expect(user.tempBanReason).to.equal('text');

    appeal = await TempBanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('rejected');
    expect(appeal.notes).to.equal('is truly inappropriate');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[0].action).to.equal('tempBan');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[1].action).to.equal('tempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('tempBanAppealRejected');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].notes).to.equal('is truly inappropriate');
  });

  it('approve temp ban appeal', async function () {
    user = await User.findById('0');

    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'approved', notes: 'is not inappropriate' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.banNotice).to.equal();
    expect(res.body.user.accountRestrictions).to.eql({});

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.tempBanReason).to.equal();
    expect(user.tempBanEndAt).to.equal();

    appeal = await TempBanAppeal.findById(appealId);
    expect(appeal.reviewedBy).to.equal('1');
    expect(appeal.decision).to.equal('approved');
    expect(appeal.notes).to.equal('is not inappropriate');

    // cannot appeal again
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'definitely not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    user = await User.findById('0');
    expect(user.tempBanReason).to.equal();
    expect(user.tempBanEndAt).to.equal();
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('tempBan');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[1].action).to.equal('tempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('tempBanAppealApproved');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].notes).to.equal('is not inappropriate');
    expect(user.banHistory[3].action).to.equal('undoTempBan');
    expect(user.banHistory[3].notes).to.equal('is not inappropriate');
  });

  it('appeal status reset for new ban after rejected appeal', async function () {

    res = await request(app)
      .put('/v1/admin/tempBanAppeal/decision')
      .set('authorization', 1)
      .send({ appealId, decision: 'rejected', notes: 'is truly inappropriate' })
    expect(res.status).to.equal(200);

    let user = await User.findById('0');
    expect(user.tempBanEndAt).to.not.eql(undefined);
    expect(user.tempBanReason).to.equal('text');
    expect(user.accountRestrictions.tempBan.appealStatus).to.equal('rejected');

    res = await request(app)
      .put('/v1/admin/undoTempBan')
      .set('authorization', 1)
      .send({ user: '0', notes: 'okay' });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.tempBanReason).to.equal();
    expect(user.tempBanEndAt).to.equal();
    expect(user.accountRestrictions.tempBan).to.equal();

    res = await request(app)
      .put('/v1/admin/tempBan')
      .set('authorization', 1)
      .send({ reason: 'text-2', banUser: '0' });
    expect(res.status).to.equal(200);
    
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.tempBan.appealStatus).to.equal('allowed');

    // Verify user can now appeal the new ban
    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 0)
      .send({ comment: 'this new ban is not appropriate' })
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.accountRestrictions.tempBan.appealStatus).to.equal('pending');

    const appeals = await TempBanAppeal.find({ user: '0' }).sort('createdAt');
    expect(appeals.length).to.equal(2);
    expect(appeals[1].bannedReasons).to.eql(['text-2']);
  });

  it('account restrictions initialization for non GDPR users', async function () {
    // Test user without accountRestrictions in non GDPR country
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '**********') // Panama
      .send({
        appVersion: '1.11.46'
      })
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql();

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({});

    res = await request(app)
      .put('/v1/admin/tempBan')
      .set('authorization', 1)
      .send({ reason: 'text', banUser: '2' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .set('X-Forwarded-For', '**********') // Panama
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions).to.eql();

    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 2)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(403);

    user = await User.findById('2');
    user.metrics.revenue = 1
    await user.save()

    res = await request(app)
      .put('/v1/user/tempBanAppeal')
      .set('authorization', 2)
      .send({ comment: 'not inappropriate' })
    expect(res.status).to.equal(403);

    res = await request(app)
      .get('/v1/user/accountRestrictions')
      .set('authorization', 2)
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({});

  });

  it('dismiss pending appeal if user unbanned before appeal reviewed', async function () {
    res = await request(app)
      .put('/v1/admin/undoTempBan')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.tempBan).to.equal();

    appeals = await TempBanAppeal.find();
    expect(appeals.length).to.equal(1);
    appeal = appeals[0];
    expect(appeal.reviewedBy).to.equal();
    expect(appeal.reviewedAt).to.equal();
    expect(appeal.decision).to.equal('dismissed');
    expect(appeal.notes).to.equal('user was unbanned before appeal was reviewed');

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('tempBan');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('text');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('tempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('undoTempBan');
    expect(user.banHistory[2].by).to.equal('1');
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal();
    expect(user.banHistory[3].action).to.equal('tempBanAppealDismissed');
    expect(user.banHistory[3].by).to.equal();
    expect(user.banHistory[3].reason).to.equal();
    expect(user.banHistory[3].notes).to.equal('user was unbanned before appeal was reviewed');
  });

  it('dont return pending appeal for admin if time is already expired', async function () {

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.tempBan.appealStatus).to.equal('pending');
    expect(res.body.user.tempBanReason).to.equal('text');

    let clock = sinon.useFakeTimers({ now: Date.now() });
    clock.tick(1*24*60*60*1000);

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.tempBan).to.equal();

    appeals = await TempBanAppeal.find();
    expect(appeals.length).to.equal(1);
    appeal = appeals[0];
    expect(appeal.reviewedBy).to.equal();
    expect(appeal.reviewedAt).to.equal();
    expect(appeal.decision).to.equal('dismissed');
    expect(appeal.notes).to.equal('user was unbanned before appeal was reviewed');

    user = await User.findById('0');
    console.log(user.banHistory);
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('tempBan');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('text');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('tempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('undoTempBan');
    expect(user.banHistory[2].by).to.equal();
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal('auto system temp ban end detected and unbanned');
    expect(user.banHistory[3].action).to.equal('tempBanAppealDismissed');
    expect(user.banHistory[3].by).to.equal();
    expect(user.banHistory[3].reason).to.equal();
    expect(user.banHistory[3].notes).to.equal('user was unbanned before appeal was reviewed');

    clock.restore()
  });

  it('dismiss pending appeal if time is already expired', async function () {

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.tempBan.appealStatus).to.equal('pending');
    expect(res.body.user.tempBanReason).to.equal('text');

    let clock = sinon.useFakeTimers({ now: Date.now() });
    clock.tick(1*24*60*60*1000);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.tempBanReason).to.equal();
    expect(res.body.user.accountRestrictions.tempBan).to.equal();

    res = await request(app)
      .get('/v1/admin/tempBanAppeal')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.appeals.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.accountRestrictions.tempBan).to.equal();

    appeals = await TempBanAppeal.find();
    expect(appeals.length).to.equal(1);
    appeal = appeals[0];
    expect(appeal.reviewedBy).to.equal();
    expect(appeal.reviewedAt).to.equal();
    expect(appeal.decision).to.equal('dismissed');
    expect(appeal.notes).to.equal('user was unbanned before appeal was reviewed');

    user = await User.findById('0');
    expect(user.banHistory.length).to.equal(4);
    expect(user.banHistory[0].action).to.equal('tempBan');
    expect(user.banHistory[0].by).to.equal('1');
    expect(user.banHistory[0].reason).to.equal('text');
    expect(user.banHistory[0].notes).to.equal();
    expect(user.banHistory[1].action).to.equal('tempBanAppealSubmitted');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].reason).to.equal();
    expect(user.banHistory[1].notes).to.equal('not inappropriate');
    expect(user.banHistory[2].action).to.equal('undoTempBan');
    expect(user.banHistory[2].by).to.equal();
    expect(user.banHistory[2].reason).to.equal();
    expect(user.banHistory[2].notes).to.equal('auto system temp ban end detected and unbanned');
    expect(user.banHistory[3].action).to.equal('tempBanAppealDismissed');
    expect(user.banHistory[3].by).to.equal();
    expect(user.banHistory[3].reason).to.equal();
    expect(user.banHistory[3].notes).to.equal('user was unbanned before appeal was reviewed');

    clock.restore()
  });
});

